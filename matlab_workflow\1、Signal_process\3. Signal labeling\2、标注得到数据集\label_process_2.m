%% 改进的肠鸣音信号标注数据提取程序
% 功能描述：根据标注文件中的时间范围信息，从原始信号数据中提取对应片段并分类保存
% 主要改进：
% 1. 正确处理labeledSignalSet数据结构
% 2. 实现时间轴还原功能（将分段时间还原为连续时间）
% 3. 支持多通道信号提取（tt1和tt2）
% 4. 改进的文件命名和组织结构
%
% 输入说明：
% - 标注文件：ls_data3.mat，包含labeledSignalSet对象
% - 原始数据：1、Raw data文件夹中的分段信号文件
%
% 输出说明：
% - 提取的数据保存到2、Processed data文件夹
% - 包含原始时间、还原时间、信号数据和标签信息
%
% 时间轴还原规则：
% - seg001: 0-60秒 → 保持0-60秒
% - seg002: 0-60秒 → 还原为60-120秒
% - seg003: 0-60秒 → 还原为120-180秒
% - 以此类推

clear;
clc;
close all;

%% 配置路径
labelFile = '4、Label/ls_data3.mat';
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

%% 加载标注文件
fprintf('正在加载标注文件: %s\n', labelFile);
load(labelFile);

% 获取标注信息
labels = ls.Labels;
fprintf('找到 %d 个标注文件\n', height(labels));

%% 提取标注数据的主循环
extractedCount = 0;
totalLabels = 0;

for i = 1:height(labels)
    % 获取当前行的文件名和标注表
    signalName = labels.Row{i};  % 例如：'data3_5min_seg002_tt1'
    bsTable = labels{i, 'BS'}{1}; % 获取标注表

    % 检查是否有标注数据
    if isempty(bsTable) || height(bsTable) == 0
        continue;
    end

    fprintf('\n处理信号: %s\n', signalName);
    fprintf('找到 %d 个标注\n', height(bsTable));

    % 解析文件名以获取数据集、段号和通道信息
    [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);

    % 构造对应的原始数据文件路径
    rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
    rawFilePath = fullfile(rawDataDir, rawFileName);

    if ~exist(rawFilePath, 'file')
        warning('原始数据文件不存在: %s', rawFilePath);
        continue;
    end

    % 加载原始数据
    fprintf('加载原始数据: %s\n', rawFileName);
    rawData = load(rawFilePath);

    % 获取对应通道的数据
    % 标注文件中的signalName就是变量名，例如：'data3_5min_seg002_tt1'
    if ~isfield(rawData, signalName)
        warning('信号变量不存在: %s', signalName);
        continue;
    end

    signalData = rawData.(signalName);

    % 处理每个标注
    for j = 1:height(bsTable)
        totalLabels = totalLabels + 1;

        % 获取标注信息
        roiLimits = bsTable.ROILimits(j, :);
        labelValue = bsTable.Value(j);
        if iscell(labelValue)
            labelValue = labelValue{1};
        end

        % 计算还原后的真实时间
        segmentOffset = (segmentNum - 1) * 60; % 每段60秒
        realStartTime = roiLimits(1) + segmentOffset;
        realEndTime = roiLimits(2) + segmentOffset;

        fprintf('  标注 %d: %.3f-%.3f秒 (原始) → %.3f-%.3f秒 (还原), 标签: %s\n', ...
            j, roiLimits(1), roiLimits(2), realStartTime, realEndTime, labelValue);

        % 提取信号片段
        try
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);

            % 创建还原时间的时间表
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);

            % 创建包含还原时间的新时间表，保持Time作为时间列名
            restoredSignal = extractedSignal;  % 复制原始结构
            restoredSignal.Time = restoredTime;  % 更新时间列

            % 准备保存的数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;      % 原始时间的信号
            extractedData.restoredSignal = restoredSignal;       % 还原时间的信号
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;

            % 构造输出文件名
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(outputDir, outputFileName);

            % 保存数据
            save(outputFilePath, 'extractedData');
            extractedCount = extractedCount + 1;

            fprintf('    已保存: %s\n', outputFileName);

        catch ME
            warning('SIGNAL_EXTRACTION:Error', '提取信号时出错: %s', ME.message);
        end
    end
end

%% 生成处理报告
fprintf('\n=== 处理完成 ===\n');
fprintf('总标注数量: %d\n', totalLabels);
fprintf('成功提取: %d\n', extractedCount);
fprintf('输出目录: %s\n', outputDir);

% 保存处理报告
reportData = struct();
reportData.processTime = datetime('now');
reportData.totalLabels = totalLabels;
reportData.extractedCount = extractedCount;
reportData.sourceFile = labelFile;
reportData.outputDir = outputDir;

save(fullfile(outputDir, 'extraction_report.mat'), 'reportData');
fprintf('处理报告已保存: %s\n', fullfile(outputDir, 'extraction_report.mat'));

%% 辅助函数：解析信号名称
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    % 解析信号名称，例如：'data3_5min_seg002_tt1'
    % 返回：datasetInfo = 'data3_5min', segmentNum = 2, channelInfo = 'tt1'

    % 使用正则表达式解析
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');

    if isempty(tokens)
        error('无法解析信号名称: %s', signalName);
    end

    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end



